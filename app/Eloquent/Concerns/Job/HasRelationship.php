<?php

namespace App\Eloquent\Concerns\Job;

use App\Eloquent\Group;
use App\Eloquent\JobApplication;
use App\Eloquent\JobCategory;
use App\Eloquent\JobImage;
use App\Eloquent\StorageFile;
use App\Eloquent\User;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

trait HasRelationship
{
    public function thumbnail()
    {
        return $this->belongsTo(StorageFile::class, 'thumbnail_id');
    }

    public function category()
    {
        return $this->belongsTo(JobCategory::class, 'category_id');
    }

    public function jobApplications()
    {
        return $this->hasMany(JobApplication::class, 'job_id');
    }

    /**
     * @return HasMany
     */
    public function images(): HasMany
    {
        return $this->hasMany(JobImage::class, 'job_id');
    }

    public function favorites()
    {
        return $this->belongsToMany(User::class, 't_user_favorites');
    }

    public function jobAppliers()
    {
        return $this->belongsToMany(User::class, 't_job_applications',  'job_id',  'user_id')
            ->withPivot('approval_status')->withTimestamps();
    }

    /**
     * @return BelongsTo
     */
    public function group(): BelongsTo
    {
        return $this->belongsTo(Group::class, 'group_id');
    }
}
