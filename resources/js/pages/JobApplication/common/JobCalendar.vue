<script setup lang="ts">
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/24/solid';
import FullCalendar from '@/components/common/shared/FullCalendar.vue';
import Button from '@/components/common/shared/Button.vue';
import { computed, ref } from 'vue';
import { router, usePage } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n.ts';
import { useRoute } from 'ziggy-js';
import { format, parseISO, isValid } from 'date-fns';
import { ListJobType } from '@/types/job.ts';
import Modal from '@/components/common/Modal.vue';

const props = defineProps<{
  jobs: ListJobType[];
}>();

const { t } = useI18n();
const route = useRoute();
const page = usePage();

const date =
  page.props.date && typeof page.props.date === 'string'
    ? isValid(parseISO(page.props.date))
      ? parseISO(page.props.date)
      : new Date()
    : new Date();
const isMonth = computed(() => {
  const urlParams = new URLSearchParams(window.location.search);
  return urlParams.get('is_month') === 'true';
});

const currentViewMonth = ref(date);
const isVisible = ref(true);

const events = computed(() => {
  return props.jobs.map(job => ({
    id: job.id,
    title: job.title,
    date: parseISO(job.recruitExpiredAt),
    timeStart: job.timeStart,
    timeEnd: job.timeEnd,
  }));
});

function toggleVisible() {
  isVisible.value = !isVisible.value;
}

const handleEventClick = payload => {
  router.get(route('admin.job-application.show', payload.event.id));
};

const getCurrentSearchParams = () => {
  const urlParams = new URLSearchParams(window.location.search);
  return {
    title: urlParams.get('title') || '',
    prefecture: urlParams.get('prefecture') || '',
    time_start: urlParams.get('time_start') || '',
    time_end: urlParams.get('time_end') || '',
    is_filled: urlParams.get('is_filled') || '',
  };
};

const handleDayClick = (payload: { date: Date; events: object }) => {
  const searchParams = getCurrentSearchParams();
  const params: any = {
    date: format(payload.date, 'yyyy-MM-dd'),
    ...searchParams,
  };

  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key];
    }
  });

  router.get(route('admin.job-application.index'), params);
};

const handleMonthChange = async (payload: { month: Date }) => {
  currentViewMonth.value = payload.month;
  const searchParams = getCurrentSearchParams();
  const params: any = {
    date: format(payload.month, 'yyyy-MM-dd'),
    is_month: true,
    ...searchParams,
  };

  Object.keys(params).forEach(key => {
    if (params[key] === '' || params[key] === null || params[key] === undefined) {
      delete params[key];
    }
  });

  router.get(route('admin.job-application.index'), params);
};

interface CalendarJob {
  id: string | number;
  title: string;
  date: Date;
  [key: string]: any;
}

// Modal state for showing events
const isModalVisible = ref(false);
const modalEvents = ref<CalendarJob[]>([]);
const modalDate = ref<Date | null>(null);
const handleMoreClick = (payload: { date: Date; events: CalendarJob[] }) => {
  modalDate.value = payload.date;
  modalEvents.value = payload.events;
  isModalVisible.value = true;
};

const closeModal = () => {
  isModalVisible.value = false;
  modalEvents.value = [];
  modalDate.value = null;
};

const handleModalEventClick = (event: CalendarJob) => {
  if (modalDate.value) {
    router.get(route('admin.job-application.show', event.id));
  }
  closeModal();
};

const formattedModalDate = computed(() => {
  if (!modalDate.value) return '';
  return format(modalDate.value, 'yyyy-MM-dd');
});
</script>

<template>
  <div class="rounded-2xl border border-gray-200 bg-white mb-6">
    <div class="flex items-center justify-between p-4">
      <h4 class="text-lg font-semibold text-gray-800">
        {{ t('common.calendar.title') }}
      </h4>
      <Button variant="ghost" size="sm" class="text-gray-500" @click="toggleVisible">
        <ChevronUpIcon v-if="isVisible" class="w-5 h-5" />
        <ChevronDownIcon v-else class="w-5 h-5" />
      </Button>
    </div>
    <div class="custom-calendar" v-show="isVisible">
      <FullCalendar
        ref="calendarRef"
        :events="events"
        :initialMonth="currentViewMonth"
        :day-selected="!isMonth && currentViewMonth"
        @event-click="handleEventClick"
        @day-click="handleDayClick"
        @month-change="handleMonthChange"
        @more-click="handleMoreClick"
      />
    </div>
  </div>
  <Modal
    v-if="isModalVisible"
    :title="`${t('common.calendar.eventsOn')} ${formattedModalDate}`"
    width="w-full max-w-xl"
    @close="closeModal"
  >
    <template #body>
      <div class="space-y-3">
        <div v-if="modalEvents.length === 0" class="text-center text-gray-500 py-8">
          {{ t('common.calendar.noEventsFound') }}
        </div>
        <div
          v-for="event in modalEvents"
          :key="event.id"
          class="p-4 border border-gray-200 rounded-lg hover:bg-gray-50 cursor-pointer transition-colors"
          @click="handleModalEventClick(event)"
        >
          <div class="flex items-start justify-between">
            <div class="flex-1">
              <h3 class="font-medium text-gray-900 mb-1">{{ event.title }}</h3>
              <p class="text-sm text-gray-600">{{ event.timeStart }} - {{ event.timeEnd }}</p>
            </div>
          </div>
        </div>
      </div>
    </template>
  </Modal>
</template>

<style scoped></style>
