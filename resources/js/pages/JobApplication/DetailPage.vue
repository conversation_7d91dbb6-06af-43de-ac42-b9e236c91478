<script setup lang="ts">
import { Head, useForm } from '@inertiajs/vue3';
import { useI18n } from '@/composables/useI18n.ts';
import { JobType } from '@/types/job.ts';
import Button from '@/components/common/shared/Button.vue';
import { ChevronDownIcon, ChevronUpIcon } from '@heroicons/vue/24/solid';
import { CheckCircleIcon, XCircleIcon } from '@heroicons/vue/24/outline';
import { ref } from 'vue';
import VInput from '@/components/common/shared/VInput.vue';
import VSelect from '@/components/common/shared/VSelect.vue';
import VCheckbox from '@/components/common/shared/VCheckbox.vue';
import { Appliers } from '@/types/job-application.ts';
import VPagination from '@/components/common/shared/VPagination.vue';
import { route } from 'ziggy-js';
import ButtonLink from '@/components/common/shared/ButtonLink.vue';

const { t } = useI18n();

const props = defineProps<{
  job: JobType;
  appliers: Appliers;
}>();

const isSearchVisible = ref(false);

const genderOptions = (window as any).genderOptions;
const approvalOptions = (window as any).approvalSearchOptions;
const toggleSearchForm = () => {
  isSearchVisible.value = !isSearchVisible.value;
};

const searchForm = useForm({
  age_start: '',
  age_end: '',
  approval_status: '',
  gender: '',
});
</script>

<template>
  <Head :title="t('models/job_application.title')" />
  <div class="flex flex-wrap items-center justify-between gap-3 mb-6">
    <h2 class="text-xl font-semibold text-gray-800">
      {{ t('models/job_application.screenName.detail') }}
    </h2>
  </div>
  <div class="rounded-2xl border border-gray-200 bg-white p-5 lg:p-6">
    <div class="p-4 mb-6 border border-gray-200 rounded-2xl">
      <div class="w-full">
        <h4 class="text-lg font-semibold text-gray-800 mb-6">
          {{ t('models/job.screenName.detail') }}
        </h4>
        <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7 lg:gap-y-4">
          <div class="lg:col-span-2">
            <img :src="job.thumbnailUrl" alt="Thumbnail" class="h-28" />
          </div>
          <div>
            <label class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/job.field.title') }}</label>
            <p class="text-sm font-medium text-gray-800">{{ job.title }}</p>
          </div>
          <div>
            <label class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/job.field.category') }}</label>
            <p class="text-sm font-medium text-gray-800">{{ job.categoryName }}</p>
          </div>
          <div>
            <label class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/job.field.prefecture') }}</label>
            <p class="text-sm font-medium text-gray-800">{{ job.prefecture }}</p>
          </div>
          <div>
            <label class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/job.field.quantity') }}</label>
            <p class="text-sm font-medium text-gray-800">{{ job.quantity }}</p>
          </div>
          <div>
            <label class="mb-2 text-xs leading-normal text-gray-500">{{ t('models/job.field.salary') }}</label>
            <p class="text-sm font-medium text-gray-800">{{ job.salary }}</p>
          </div>
        </div>
      </div>
    </div>
    <div class="p-4 mb-6 border border-gray-200 rounded-2xl">
      <div class="w-full">
        <div class="flex items-center justify-between" :class="isSearchVisible ? 'mb-6' : ''">
          <h4 class="text-lg font-semibold text-gray-800">
            {{ t('common.field.search') }}
          </h4>
          <Button variant="ghost" size="sm" class="text-gray-500" @click="toggleSearchForm">
            <ChevronUpIcon v-if="isSearchVisible" class="w-5 h-5" />
            <ChevronDownIcon v-else class="w-5 h-5" />
          </Button>
        </div>
        <form @submit.prevent="" v-show="isSearchVisible" class="transition-all duration-300">
          <div class="grid grid-cols-1 gap-4 lg:grid-cols-2 lg:gap-7">
            <div class="col-span-1">
              <label class="mb-1.5 block text-sm font-medium text-gray-700">{{ t('models/job.field.age') }}</label>
              <div class="grid grid-cols-2 gap-4 items-center">
                <VInput v-model="searchForm.age_start" />
                <VInput v-model="searchForm.age_end" />
              </div>
            </div>
            <VSelect
              :label="t('models/job_application.field.approval_status')"
              v-model="searchForm.approval_status"
              :options="approvalOptions"
            />
            <VCheckbox :options="genderOptions" :label="t('models/user.field.gender')" v-model="searchForm.gender" />
          </div>
          <div class="flex items-center justify-center gap-2 mt-6">
            <Button size="sm" variant="outline" @click="$emit('reset')">
              {{ t('common.btn.reset') }}
            </Button>
            <Button size="sm" variant="primary" type="submit">
              {{ t('common.btn.search') }}
            </Button>
          </div>
        </form>
      </div>
    </div>
    <div class="inline-block min-w-full align-middle">
      <div class="overflow-hidden border rounded-lg">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.id') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.name') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.email') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.gender') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.age') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.job_participation_count') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/user.field.phone_number') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('models/job_application.field.approval_status') }}
              </th>
              <th scope="col" class="p-3 text-xs font-medium tracking-wider text-left text-gray-500 uppercase">
                {{ t('common.field.action') }}
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="applier in appliers.data" :key="applier.id" class="border-t border-gray-100">
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.id }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.name }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.email }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.genderName }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.age }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.jobParticipationCount }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.phoneNumber }}</span>
              </td>
              <td class="border-t">
                <span class="px-3 py-4 text-sm">{{ applier.jobApprovalStatusName }}</span>
              </td>
              <td class="px-3 py-4 text-sm">
                <div class="flex justify-center">
                  <CheckCircleIcon class="size-5" />
                  <XCircleIcon class="size-5" />
                </div>
              </td>
            </tr>
            <tr v-if="appliers.data.length === 0">
              <td class="px-6 py-4 border-t" colspan="6">{{ t('common.noResult') }}</td>
            </tr>
          </tbody>
        </table>
      </div>
      <div class="mt-2" v-if="appliers.data.length > 0">
        <v-pagination :paginator="appliers.paginator" />
      </div>
    </div>
    <div class="flex justify-center space-x-2">
      <ButtonLink size="sm" variant="outline" :href="route('admin.job.index')">
        {{ t('common.btn.back') }}
      </ButtonLink>
    </div>
  </div>
</template>

<style scoped></style>
